package com.wishforthecure.forconversations.hexa.application.service;

import org.jmolecules.ddd.annotation.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.AssignFeelingUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AssignFeelingService implements AssignFeelingUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> assignFeeling(ConversationId conversationId, FeelingDomain feeling) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.assignFeeling(feeling);
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}
