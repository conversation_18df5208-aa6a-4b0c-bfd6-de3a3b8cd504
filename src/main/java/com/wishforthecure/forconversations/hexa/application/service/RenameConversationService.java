package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.RenameConversationUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class RenameConversationService implements RenameConversationUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> rename(ConversationId conversationId, String newName) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.rename(newName);
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}