package com.wishforthecure.forconversations.hexa.application.service;

import org.jmolecules.ddd.annotation.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.RemoveTagUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class RemoveTagService implements RemoveTagUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> removeTag(ConversationId conversationId, String tagName) {
        return conversationRepository.findById(conversationId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.removeTag(TagDomain.of(tagName));
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }
}
